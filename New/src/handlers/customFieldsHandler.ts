/**
 * Custom Fields Synchronization Handler
 *
 * HTTP handler for custom field synchronization between AutoPatient (AP)
 * and CliniCore (CC) platforms. This module contains only the HTTP request
 * handler function, with all business logic extracted to dedicated processor
 * modules for better maintainability and testability.
 *
 * Features:
 * - Clean HTTP request/response handling
 * - Request ID extraction and correlation
 * - Error handling and response formatting
 * - Integration with custom fields processor modules
 *
 * @fileoverview HTTP handler for custom field synchronization
 * @version 2.0.0
 * @since 2024-07-27
 */

import type { Context } from "hono";
import { synchronizeCustomFields } from "@/processors/customFields";
import { logError } from "@/utils/logger";

/**
 * Handle Custom Fields webhook events
 *
 * HTTP handler function that processes custom field synchronization requests.
 * Extracts the request ID from the Hono context, delegates the synchronization
 * logic to the processor module, and returns a properly formatted JSON response.
 *
 * This handler is designed to be:
 * - **Lightweight**: Contains only HTTP-specific logic
 * - **Focused**: Single responsibility for request/response handling
 * - **Traceable**: Includes request ID correlation for debugging
 * - **Resilient**: Comprehensive error handling with proper HTTP status codes
 *
 * @param c - Hono context object containing request data and utilities
 * @returns Promise resolving to HTTP Response with synchronization results
 *
 * @example
 * ```typescript
 * // Used in Hono route definition
 * app.post('/webhooks/custom-fields', cfHandler);
 *
 * // Response format on success (200):
 * {
 *   "message": "Custom field synchronization completed",
 *   "requestId": "req-123",
 *   "timestamp": "2024-07-27T10:30:00.000Z",
 *   "result": {
 *     "matchedCount": 15,
 *     "createdCcFields": [...],
 *     "statistics": {...},
 *     // ... other sync results
 *   }
 * }
 *
 * // Response format on error (500):
 * {
 *   "error": "Custom field synchronization failed",
 *   "details": "Error details...",
 *   "requestId": "req-123",
 *   "timestamp": "2024-07-27T10:30:00.000Z"
 * }
 * ```
 *
 * @since 1.0.0
 */
export async function cfHandler(c: Context): Promise<Response> {
	const requestId = c.get("requestId");

	try {
		const syncResult = await synchronizeCustomFields(requestId);

		return c.json(
			{
				message: "Custom field synchronization completed",
				requestId,
				timestamp: new Date().toISOString(),
				result: syncResult,
			},
			200,
		);
	} catch (error) {
		logError("Custom field handler failed", error);
		return c.json(
			{
				error: "Custom field synchronization failed",
				details: String(error),
				requestId,
				timestamp: new Date().toISOString(),
			},
			500,
		);
	}
}
